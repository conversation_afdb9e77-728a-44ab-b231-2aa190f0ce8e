<template>
  <div class="essay-requirements-page">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <div class="signal-bars">
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
        </div>
      </div>
      <div class="status-right">
        <div class="battery-indicator">
          <div class="battery-level"></div>
        </div>
      </div>
    </div>

    <!-- 返回按钮和标题 -->
    <div class="header">
      <div class="back-button" @click="goBack">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M15 18L9 12L15 6" stroke="#171A1F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
      <div class="page-title">作文要求</div>
    </div>

    <!-- 作文信息区域 -->
    <div class="essay-info-section">
      <div class="essay-tags">
        <span class="unit-tag">第三单元</span>
        <span class="type-tag">单元作文</span>
        <span class="category-tag">全命题</span>
      </div>
    </div>

    <!-- 作文详情 -->
    <div class="essay-details">
      <div class="detail-item">
        <span class="label">作文命题</span>
        <span class="value">我的植物朋友</span>
      </div>
      
      <div class="detail-item">
        <span class="label">字数要求</span>
        <span class="value">300字</span>
      </div>
      
      <div class="detail-item">
        <span class="label">总分</span>
        <span class="value">30分</span>
      </div>
      
      <div class="detail-item requirements">
        <span class="label">作文要求</span>
        <div class="requirements-text">
          选择一项自己做过的小实验（可以是科学课上的，也可以是自己在家尝试的），按照"实验准备—实验过程—实验结果"的顺序写下来。重点把实验过程写清楚，可以用上"先……接着……然后……最后……"等表示顺序的词语。
        </div>
      </div>
    </div>

    <!-- 上传作文区域 -->
    <div class="upload-section">
      <div class="upload-title">
        <span>上传作文</span>
        <div class="warning-icon">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <circle cx="8" cy="8" r="7" fill="#9095A0"/>
            <path d="M8 4V8" stroke="white" stroke-width="2" stroke-linecap="round"/>
            <circle cx="8" cy="12" r="1" fill="white"/>
          </svg>
        </div>
      </div>
      
      <!-- 上传区域 -->
      <div class="upload-grid">
        <div class="upload-item" v-for="(item, index) in uploadItems" :key="index">
          <div class="upload-box" :class="{ 'has-content': item.hasContent }">
            <div v-if="!item.hasContent" class="upload-placeholder">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#BCC1CA" stroke-width="2">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                <polyline points="14,2 14,8 20,8"/>
                <line x1="16" y1="13" x2="8" y2="13"/>
                <line x1="16" y1="17" x2="8" y2="17"/>
                <polyline points="10,9 9,9 8,9"/>
              </svg>
            </div>
            <div v-else class="upload-content">
              <!-- 这里可以显示已上传的内容 -->
            </div>
          </div>
          <div v-if="item.hasContent" class="remove-button">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <circle cx="8" cy="8" r="7" stroke="#DE3B40" stroke-width="2"/>
              <line x1="5.33" y1="8" x2="10.67" y2="8" stroke="#DE3B40" stroke-width="2"/>
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- 提交按钮 -->
    <div class="submit-section">
      <button class="submit-button" @click="submitEssay">
        提交
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EssayRequirementsPage',
  emits: ['back'],
  data() {
    return {
      uploadItems: [
        { hasContent: false },
        { hasContent: false },
        { hasContent: false },
        { hasContent: false }
      ]
    }
  },
  methods: {
    goBack() {
      this.$emit('back')
    },
    submitEssay() {
      console.log('提交作文')
      // 这里可以添加提交逻辑
    }
  }
}
</script>

<style scoped>
.essay-requirements-page {
  width: 375px;
  height: 844px;
  background: #FFFFFF;
  position: relative;
  margin: 0 auto;
  box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
  font-family: 'Inter', sans-serif;
  overflow-y: auto;
}

/* 状态栏 */
.status-bar {
  width: 100%;
  height: 40px;
  background: transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  box-sizing: border-box;
}

.status-left {
  display: flex;
  align-items: center;
}

.signal-bars {
  display: flex;
  gap: 2px;
  align-items: flex-end;
}

.bar {
  width: 2.55px;
  background: #000000;
}

.bar:nth-child(1) { height: 3.4px; }
.bar:nth-child(2) { height: 5.53px; }
.bar:nth-child(3) { height: 8.51px; }
.bar:nth-child(4) { height: 10.21px; }

.status-right {
  display: flex;
  align-items: center;
}

.battery-indicator {
  width: 20.28px;
  height: 10.06px;
  border: 1px solid #000000;
  border-radius: 2px;
  position: relative;
  opacity: 0.35;
}

.battery-level {
  width: 17.87px;
  height: 7.66px;
  background: #000000;
  position: absolute;
  top: 1.2px;
  left: 1.21px;
}

/* 头部 */
.header {
  display: flex;
  align-items: center;
  padding: 8px 24px;
  position: relative;
}

.back-button {
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 16px;
  line-height: 26px;
  color: #323842;
  font-weight: 400;
}

/* 作文信息区域 */
.essay-info-section {
  background: #F8F9FA;
  padding: 8px 26px;
}

.essay-tags {
  display: flex;
  gap: 12px;
}

.unit-tag, .type-tag, .category-tag {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
}

/* 作文详情 */
.essay-details {
  padding: 18px 26px;
}

.detail-item {
  display: flex;
  margin-bottom: 26px;
}

.detail-item.requirements {
  flex-direction: column;
  align-items: flex-start;
}

.label {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  width: 44px;
  flex-shrink: 0;
  margin-right: 12px;
}

.value {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
}

.requirements-text {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  margin-top: 0;
  margin-left: 56px;
  max-width: 263px;
}

/* 上传区域 */
.upload-section {
  background: #F8F9FA;
  padding: 20px 24px;
}

.upload-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 13px;
  font-size: 11px;
  line-height: 18px;
  color: #323842;
}

.warning-icon {
  width: 16px;
  height: 16px;
}

.upload-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.upload-item {
  position: relative;
}

.upload-box {
  width: 131px;
  height: 76px;
  border: 3px solid #BCC1CA;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #FFFFFF;
  cursor: pointer;
}

.upload-box.has-content {
  border: 1px solid #DEE1E6;
  border-style: dashed;
}

.upload-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-button {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 16px;
  height: 16px;
  cursor: pointer;
}

/* 提交按钮 */
.submit-section {
  padding: 20px 12px;
}

.submit-button {
  width: 350px;
  height: 52px;
  background: #636AE8;
  border: none;
  border-radius: 16px;
  color: #FFFFFF;
  font-size: 18px;
  line-height: 28px;
  cursor: pointer;
  font-family: 'Inter', sans-serif;
}

.submit-button:hover {
  background: #5258d6;
}
</style>
